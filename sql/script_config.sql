-- 脚本配置表
CREATE TABLE `script_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `code` varchar(100) NOT NULL COMMENT '脚本代码标识符',
  `script` text NOT NULL COMMENT '实际执行的SQL/InfluxQL脚本模板',
  `description` varchar(500) DEFAULT NULL COMMENT '脚本描述信息',
  `db_type` varchar(20) NOT NULL COMMENT '数据库类型（MYSQL/INFLUXDB）',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '1' COMMENT '删除标志（0代表删除 1代表存在）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_script_code` (`code`) COMMENT '脚本代码唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='脚本配置表';

-- 插入示例数据
INSERT INTO `script_config` (`code`, `script`, `description`, `db_type`) VALUES
('QUERY_ENERGY_BY_DATE', 'SELECT * FROM ems_energy WHERE create_time >= #{startDate} AND create_time <= #{endDate}', '根据日期范围查询能源数据', 'MYSQL'),
('QUERY_DEVICE_BY_STATUS', 'SELECT * FROM ems_device WHERE status = #{status} AND energy_id IN #{energyIds}', '根据状态和能源ID列表查询设备', 'MYSQL'),
('QUERY_MEASURE_BY_CENTER', 'SELECT m.*, e.energy_name, c.center_name FROM ems_measure m LEFT JOIN ems_energy e ON m.energy_med = e.energy_id LEFT JOIN ems_cost_center c ON m.cost_center_id = c.center_id WHERE m.cost_center_id = #{costCenterId} AND m.status = #{status}', '根据成本中心查询计量器具', 'MYSQL'),
('QUERY_FACTORY_TREE', 'SELECT * FROM ems_factory WHERE parent_id = #{parentId} AND status = #{status} ORDER BY order_num', '查询企业架构树形结构', 'MYSQL'),
('INFLUX_ENERGY_TREND', 'from(bucket: "#{bucket}") |> range(start: #{startTime}, stop: #{endTime}) |> filter(fn: (r) => r["_measurement"] == "energy_data") |> filter(fn: (r) => r["device_id"] == "#{deviceId}")', '查询设备能耗趋势数据', 'INFLUXDB'),
('INFLUX_POWER_STATS', 'from(bucket: "#{bucket}") |> range(start: #{startTime}) |> filter(fn: (r) => r["_measurement"] == "power_data") |> aggregateWindow(every: 1h, fn: mean)', '查询功率统计数据', 'INFLUXDB'),
('INFLUX_DAILY_REPORT', 'from(bucket: "#{bucket}") |> range(start: #{startTime}, stop: #{endTime}) |> filter(fn: (r) => r["_measurement"] == "#{measurement}") |> filter(fn: (r) => r["sampler"] == "#{sampler}") |> aggregateWindow(every: 1d, fn: sum)', '查询日报数据', 'INFLUXDB');
