# 动态脚本查询功能实现

## 任务概述
在 ScriptQueryController 中新增一个动态脚本查询接口，实现根据预定义脚本代码查询 MySQL 或 InfluxDB 的功能。

## 实施方案
采用双数据源适配器方案，为MySQL和InfluxDB分别创建查询适配器，统一接口调用。

## 执行计划

### 第一阶段：数据库设计与实体创建
1. 创建script_config表的SQL脚本
2. 创建ScriptConfig实体类
3. 创建ScriptConfigMapper接口

### 第二阶段：查询适配器设计
4. 创建QueryAdapter接口
5. 创建MySQLQueryAdapter实现类
6. 创建InfluxDBQueryAdapter实现类

### 第三阶段：参数处理器
7. 创建ParameterProcessor工具类
8. 创建SQL注入防护工具类

### 第四阶段：Service层实现
9. 创建IScriptQueryService接口
10. 创建ScriptQueryServiceImpl实现类

### 第五阶段：Controller层实现
11. 完善ScriptQueryController
12. 创建请求和响应DTO类

### 第六阶段：配置和安全
13. 添加查询超时配置
14. 实现权限验证

### 第七阶段：测试数据和文档
15. 创建测试SQL脚本
16. 创建单元测试

## 技术要求
- 支持参数占位符 #{paramName}
- 统一返回格式（success、message、data、total）
- SQL注入防护
- 脚本执行权限验证
- 查询超时控制

## 当前状态
正在执行中...
