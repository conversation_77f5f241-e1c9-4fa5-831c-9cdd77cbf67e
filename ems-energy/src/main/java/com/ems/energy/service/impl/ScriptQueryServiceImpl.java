package com.ems.energy.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ems.common.exception.ServiceException;
import com.ems.common.utils.StringUtils;
import com.ems.energy.adapter.QueryAdapter;
import com.ems.energy.config.ScriptQueryProperties;
import com.ems.energy.domain.ScriptConfig;
import com.ems.energy.domain.dto.ScriptQueryResponse;
import com.ems.energy.mapper.ScriptConfigMapper;
import com.ems.energy.service.IScriptQueryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 脚本查询Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class ScriptQueryServiceImpl implements IScriptQueryService {

    private static final Logger log = LoggerFactory.getLogger(ScriptQueryServiceImpl.class);

    @Autowired
    private ScriptConfigMapper scriptConfigMapper;

    @Autowired
    private List<QueryAdapter> queryAdapters;

    @Autowired
    private ScriptQueryProperties scriptQueryProperties;

    @Override
    public ScriptQueryResponse executeQuery(String code, Map<String, Object> params) {
        ScriptQueryResponse response = new ScriptQueryResponse();
        
        try {
            // 1. 根据code查询脚本配置
            ScriptConfig scriptConfig = getScriptConfigByCode(code);
            if (scriptConfig == null) {
                response.setSuccess(false);
                response.setMessage("脚本配置不存在：" + code);
                return response;
            }

            // 2. 获取对应的查询适配器
            QueryAdapter adapter = getQueryAdapter(scriptConfig.getDbType());
            if (adapter == null) {
                response.setSuccess(false);
                response.setMessage("不支持的数据库类型：" + scriptConfig.getDbType());
                return response;
            }

            // 3. 执行查询（带超时控制）
            List<Map<String, Object>> data = executeQueryWithTimeout(adapter, scriptConfig.getScript(), params);
            
            // 4. 构建响应结果
            response.setSuccess(true);
            response.setMessage("查询成功");
            response.setData(data);
            response.setTotal(data.size());
            
            log.info("脚本查询成功，code: {}, 结果数量: {}", code, data.size());
            
        } catch (TimeoutException e) {
            log.error("脚本查询超时，code: {}", code, e);
            response.setSuccess(false);
            response.setMessage("查询超时");
        } catch (SecurityException e) {
            log.error("脚本安全验证失败，code: {}", code, e);
            response.setSuccess(false);
            response.setMessage("脚本安全验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("脚本查询异常，code: {}", code, e);
            response.setSuccess(false);
            response.setMessage("查询异常：" + e.getMessage());
        }
        
        return response;
    }

    /**
     * 带超时控制的查询执行
     */
    private List<Map<String, Object>> executeQueryWithTimeout(QueryAdapter adapter, String script, Map<String, Object> params) 
            throws Exception {
        CompletableFuture<List<Map<String, Object>>> future = CompletableFuture.supplyAsync(() -> {
            try {
                return adapter.executeQuery(script, params);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        return future.get(scriptQueryProperties.getQueryTimeout(), TimeUnit.SECONDS);
    }

    /**
     * 获取查询适配器
     */
    private QueryAdapter getQueryAdapter(String dbType) {
        return queryAdapters.stream()
                .filter(adapter -> adapter.getSupportedDbType().equals(dbType))
                .findFirst()
                .orElse(null);
    }

    @Override
    public ScriptConfig getScriptConfigByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        return scriptConfigMapper.selectScriptConfigByCode(code);
    }

    @Override
    public List<ScriptConfig> selectScriptConfigList(ScriptConfig scriptConfig) {
        LambdaQueryWrapper<ScriptConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        if (StringUtils.isNotEmpty(scriptConfig.getCode())) {
            queryWrapper.like(ScriptConfig::getCode, scriptConfig.getCode());
        }
        if (StringUtils.isNotEmpty(scriptConfig.getDbType())) {
            queryWrapper.eq(ScriptConfig::getDbType, scriptConfig.getDbType());
        }
        if (StringUtils.isNotEmpty(scriptConfig.getDescription())) {
            queryWrapper.like(ScriptConfig::getDescription, scriptConfig.getDescription());
        }
        
        queryWrapper.orderByDesc(ScriptConfig::getCreatedTime);
        
        return scriptConfigMapper.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertScriptConfig(ScriptConfig scriptConfig) {
        // 验证脚本配置
        if (!validateScriptConfig(scriptConfig)) {
            throw new ServiceException("脚本配置验证失败");
        }
        
        // 检查代码是否已存在
        ScriptConfig existing = getScriptConfigByCode(scriptConfig.getCode());
        if (existing != null) {
            throw new ServiceException("脚本代码已存在：" + scriptConfig.getCode());
        }
        
        return scriptConfigMapper.insert(scriptConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateScriptConfig(ScriptConfig scriptConfig) {
        // 验证脚本配置
        if (!validateScriptConfig(scriptConfig)) {
            throw new ServiceException("脚本配置验证失败");
        }
        
        return scriptConfigMapper.updateById(scriptConfig);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteScriptConfigById(Long id) {
        return scriptConfigMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteScriptConfigByIds(Long[] ids) {
        return scriptConfigMapper.deleteBatchIds(List.of(ids));
    }

    @Override
    public boolean validateScriptConfig(ScriptConfig scriptConfig) {
        if (scriptConfig == null) {
            return false;
        }
        
        // 验证必填字段
        if (StringUtils.isEmpty(scriptConfig.getCode()) || 
            StringUtils.isEmpty(scriptConfig.getScript()) || 
            StringUtils.isEmpty(scriptConfig.getDbType())) {
            return false;
        }
        
        // 验证数据库类型
        if (!"MYSQL".equals(scriptConfig.getDbType()) && !"INFLUXDB".equals(scriptConfig.getDbType())) {
            return false;
        }
        
        // 验证脚本安全性
        QueryAdapter adapter = getQueryAdapter(scriptConfig.getDbType());
        if (adapter != null && !adapter.validateScript(scriptConfig.getScript())) {
            return false;
        }
        
        return true;
    }
}
