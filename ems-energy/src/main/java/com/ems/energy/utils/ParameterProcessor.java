package com.ems.energy.utils;

import com.ems.common.utils.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 参数处理器
 * 处理脚本中的参数占位符替换
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Component
public class ParameterProcessor {

    private static final Pattern PARAM_PATTERN = Pattern.compile("#\\{([^}]+)\\}");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat INFLUX_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    /**
     * 处理参数替换
     *
     * @param script 原始脚本
     * @param params 参数映射
     * @return 处理后的脚本
     */
    public String processParameters(String script, Map<String, Object> params) {
        if (script == null || params == null) {
            return script;
        }

        Matcher matcher = PARAM_PATTERN.matcher(script);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String paramName = matcher.group(1);
            Object paramValue = params.get(paramName);
            
            String replacement = formatParameterValue(paramValue);
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        
        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 格式化参数值
     *
     * @param value 参数值
     * @return 格式化后的字符串
     */
    private String formatParameterValue(Object value) {
        if (value == null) {
            return "NULL";
        }

        // 字符串类型：添加单引号
        if (value instanceof String) {
            return "'" + escapeString((String) value) + "'";
        }

        // 数值类型：直接转换
        if (value instanceof Number) {
            return value.toString();
        }

        // 布尔类型：转换为数据库格式
        if (value instanceof Boolean) {
            return ((Boolean) value) ? "1" : "0";
        }

        // 日期类型：转换为数据库格式
        if (value instanceof Date) {
            return "'" + DATE_FORMAT.format((Date) value) + "'";
        }

        // 列表类型：转换为IN子句格式
        if (value instanceof List) {
            return formatListValue((List<?>) value);
        }

        // 其他类型：转换为字符串并添加单引号
        return "'" + escapeString(value.toString()) + "'";
    }

    /**
     * 格式化列表值为IN子句格式
     *
     * @param list 列表值
     * @return IN子句格式字符串
     */
    private String formatListValue(List<?> list) {
        if (list == null || list.isEmpty()) {
            return "('')";
        }

        StringBuilder sb = new StringBuilder("(");
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            Object item = list.get(i);
            if (item instanceof String) {
                sb.append("'").append(escapeString((String) item)).append("'");
            } else if (item instanceof Number) {
                sb.append(item.toString());
            } else {
                sb.append("'").append(escapeString(item.toString())).append("'");
            }
        }
        sb.append(")");
        return sb.toString();
    }

    /**
     * 转义字符串中的特殊字符
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    private String escapeString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''")
                  .replace("\\", "\\\\");
    }

    /**
     * 格式化InfluxDB时间参数
     *
     * @param date 日期对象
     * @return InfluxDB时间格式字符串
     */
    public String formatInfluxTime(Date date) {
        if (date == null) {
            return "now()";
        }
        return INFLUX_DATE_FORMAT.format(date);
    }
}
