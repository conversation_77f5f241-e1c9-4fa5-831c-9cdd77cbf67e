package com.ems.energy.adapter.impl;

import com.ems.energy.adapter.QueryAdapter;
import com.ems.energy.utils.ParameterProcessor;
import com.ems.energy.utils.SqlSecurityUtils;
import com.influxdb.client.InfluxDBClient;
import com.influxdb.query.FluxRecord;
import com.influxdb.query.FluxTable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * InfluxDB查询适配器实现
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Component
public class InfluxDBQueryAdapter implements QueryAdapter {

    @Autowired
    private InfluxDBClient influxDBClient;

    @Autowired
    private ParameterProcessor parameterProcessor;

    @Autowired
    private SqlSecurityUtils sqlSecurityUtils;

    @Override
    public List<Map<String, Object>> executeQuery(String script, Map<String, Object> params) throws Exception {
        // 验证脚本安全性
        if (!validateScript(script)) {
            throw new SecurityException("脚本包含不安全的内容");
        }

        // 处理参数替换
        String processedScript = processParameters(script, params);

        // 执行InfluxDB查询
        List<FluxTable> tables = influxDBClient.getQueryApi().query(processedScript);
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (FluxTable table : tables) {
            for (FluxRecord record : table.getRecords()) {
                Map<String, Object> row = new HashMap<>();
                
                // 添加时间字段
                if (record.getTime() != null) {
                    row.put("_time", record.getTime());
                }
                
                // 添加值字段
                if (record.getValue() != null) {
                    row.put("_value", record.getValue());
                }
                
                // 添加测量名称
                if (record.getMeasurement() != null) {
                    row.put("_measurement", record.getMeasurement());
                }
                
                // 添加字段名
                if (record.getField() != null) {
                    row.put("_field", record.getField());
                }
                
                // 添加所有标签和字段
                // 使用getValueByKey方法获取特定字段
                if (record.getValueByKey("sampler") != null) {
                    row.put("sampler", record.getValueByKey("sampler"));
                }

                // 添加其他可能的标签字段
                String[] commonTags = {"device_id", "location", "type", "category"};
                for (String tag : commonTags) {
                    Object tagValue = record.getValueByKey(tag);
                    if (tagValue != null) {
                        row.put(tag, tagValue);
                    }
                }
                
                result.add(row);
            }
        }
        
        return result;
    }

    @Override
    public String getSupportedDbType() {
        return "INFLUXDB";
    }

    @Override
    public boolean validateScript(String script) {
        // InfluxDB Flux查询的安全性验证
        return sqlSecurityUtils.validateFluxQuery(script);
    }

    @Override
    public String processParameters(String script, Map<String, Object> params) {
        return parameterProcessor.processParameters(script, params);
    }
}
