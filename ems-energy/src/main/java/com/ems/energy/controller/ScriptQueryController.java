package com.ems.energy.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.energy.domain.ScriptConfig;
import com.ems.energy.domain.dto.ScriptQueryRequest;
import com.ems.energy.domain.dto.ScriptQueryResponse;
import com.ems.energy.service.IScriptQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 脚本查询Controller
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Api("脚本查询管理")
@RestController
@RequestMapping("/api/script")
public class ScriptQueryController extends BaseController {

    @Autowired
    private IScriptQueryService scriptQueryService;

    /**
     * 动态脚本查询接口
     */
    @ApiOperation("动态脚本查询")
    @PreAuthorize("@ss.hasRole('scriptQuery')")
    @Log(title = "脚本查询", businessType = BusinessType.OTHER)
    @PostMapping("/query")
    public AjaxResult query(@Validated @RequestBody ScriptQueryRequest request) {
        ScriptQueryResponse response = scriptQueryService.executeQuery(request.getCode(), request.getParams());

        if (response.isSuccess()) {
            return AjaxResult.success(response.getMessage(), response);
        } else {
            return AjaxResult.error(response.getMessage());
        }
    }

    /**
     * 查询脚本配置列表
     */
    @ApiOperation("查询脚本配置列表")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @GetMapping("/config/list")
    public TableDataInfo list(ScriptConfig scriptConfig) {
        startPage();
        List<ScriptConfig> list = scriptQueryService.selectScriptConfigList(scriptConfig);
        return getDataTable(list);
    }

    /**
     * 导出脚本配置列表
     */
    @ApiOperation("导出脚本配置列表")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @Log(title = "脚本配置", businessType = BusinessType.EXPORT)
    @PostMapping("/config/export")
    public void export(HttpServletResponse response, ScriptConfig scriptConfig) {
        List<ScriptConfig> list = scriptQueryService.selectScriptConfigList(scriptConfig);
        ExcelUtil<ScriptConfig> util = new ExcelUtil<>(ScriptConfig.class);
        util.exportExcel(response, list, "脚本配置数据");
    }

    /**
     * 获取脚本配置详细信息
     */
    @ApiOperation("获取脚本配置详细信息")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @GetMapping("/config/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(scriptQueryService.getScriptConfigById(id));
    }

    /**
     * 根据代码获取脚本配置
     */
    @ApiOperation("根据代码获取脚本配置")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @GetMapping("/config/code/{code}")
    public AjaxResult getInfoByCode(@PathVariable("code") String code) {
        return AjaxResult.success(scriptQueryService.getScriptConfigByCode(code));
    }

    /**
     * 新增脚本配置
     */
    @ApiOperation("新增脚本配置")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @Log(title = "脚本配置", businessType = BusinessType.INSERT)
    @PostMapping("/config")
    public AjaxResult add(@Validated @RequestBody ScriptConfig scriptConfig) {
        return toAjax(scriptQueryService.insertScriptConfig(scriptConfig));
    }

    /**
     * 修改脚本配置
     */
    @ApiOperation("修改脚本配置")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @Log(title = "脚本配置", businessType = BusinessType.UPDATE)
    @PutMapping("/config")
    public AjaxResult edit(@Validated @RequestBody ScriptConfig scriptConfig) {
        return toAjax(scriptQueryService.updateScriptConfig(scriptConfig));
    }

    /**
     * 删除脚本配置
     */
    @ApiOperation("删除脚本配置")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @Log(title = "脚本配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/config/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(scriptQueryService.deleteScriptConfigByIds(ids));
    }

    /**
     * 验证脚本配置
     */
    @ApiOperation("验证脚本配置")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @PostMapping("/config/validate")
    public AjaxResult validate(@RequestBody ScriptConfig scriptConfig) {
        boolean isValid = scriptQueryService.validateScriptConfig(scriptConfig);
        if (isValid) {
            return AjaxResult.success("脚本配置验证通过");
        } else {
            return AjaxResult.error("脚本配置验证失败");
        }
    }

    /**
     * 获取脚本查询配置信息
     */
    @ApiOperation("获取脚本查询配置信息")
    @PreAuthorize("@ss.hasRole('scriptConfig')")
    @GetMapping("/config/properties")
    public AjaxResult getProperties() {
        return AjaxResult.success(scriptQueryService.getQueryProperties());
    }
}
