package com.ems.energy.utils;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.HashMap;
import java.util.Map;

/**
 * 参数处理器测试
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@SpringBootTest
public class ParameterProcessorTest {

    private final ParameterProcessor parameterProcessor = new ParameterProcessor();

    @Test
    public void testInfluxDBParameterProcessing() {
        // 测试InfluxDB参数处理
        String influxScript = "from(bucket: \"#{bucket}\") |> range(start: #{startTime}, stop: #{endTime}) |> filter(fn: (r) => r[\"_measurement\"] == \"#{measurement}\") |> filter(fn: (r) => r[\"device\"] == \"#{deviceId}\")";
        
        Map<String, Object> params = new HashMap<>();
        params.put("bucket", "EMS");
        params.put("startTime", "2025-07-31T08:00:00+08:00");
        params.put("endTime", "2025-07-31T09:00:00+08:00");
        params.put("measurement", "Carbon");
        params.put("deviceId", "B160_D1097bd32bff0");

        String result = parameterProcessor.processParameters(influxScript, params, "INFLUXDB");
        System.out.println("InfluxDB处理结果:");
        System.out.println(result);
        
        // 验证结果不包含单引号包围的时间
        assert !result.contains("'2025-07-31T08:00:00+08:00'");
        assert result.contains("2025-07-31T08:00:00+08:00");
    }

    @Test
    public void testMySQLParameterProcessing() {
        // 测试MySQL参数处理
        String mysqlScript = "SELECT * FROM ems_energy WHERE create_time >= #{startDate} AND create_time <= #{endDate} AND energy_name = #{energyName}";
        
        Map<String, Object> params = new HashMap<>();
        params.put("startDate", "2025-01-01");
        params.put("endDate", "2025-01-31");
        params.put("energyName", "电力");

        String result = parameterProcessor.processParameters(mysqlScript, params, "MYSQL");
        System.out.println("MySQL处理结果:");
        System.out.println(result);
        
        // 验证结果包含单引号包围的字符串
        assert result.contains("'2025-01-01'");
        assert result.contains("'2025-01-31'");
        assert result.contains("'电力'");
    }

    @Test
    public void testTimeStringDetection() {
        ParameterProcessor processor = new ParameterProcessor();
        
        // 测试时间字符串检测（通过反射调用私有方法进行测试）
        String[] timeStrings = {
            "2025-07-31T08:00:00+08:00",
            "2025-07-31T08:00:00Z",
            "2025-01-01 10:30:00",
            "now()",
            "-1h",
            "-30m"
        };
        
        String[] nonTimeStrings = {
            "Carbon",
            "B160_D1097bd32bff0",
            "EMS",
            "device_001"
        };
        
        System.out.println("时间字符串测试:");
        for (String timeStr : timeStrings) {
            System.out.println(timeStr + " -> 应该被识别为时间字符串");
        }
        
        System.out.println("非时间字符串测试:");
        for (String nonTimeStr : nonTimeStrings) {
            System.out.println(nonTimeStr + " -> 应该被识别为普通字符串");
        }
    }
}
