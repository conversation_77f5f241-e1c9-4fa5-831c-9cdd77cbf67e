# 动态脚本查询API文档

## 概述
动态脚本查询功能允许通过预定义的脚本代码执行MySQL或InfluxDB查询，支持参数化查询和安全验证。

## 核心接口

### 1. 动态脚本查询
**接口地址：** `POST /api/script/query`

**请求参数：**
```json
{
  "code": "QUERY_ENERGY_BY_DATE",
  "params": {
    "startDate": "2025-01-01",
    "endDate": "2025-01-31"
  }
}
```

**响应结果：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "success": true,
    "message": "查询成功",
    "data": [
      {
        "energy_id": 1,
        "energy_code": "E001",
        "energy_name": "电力",
        "create_time": "2025-01-15 10:30:00"
      }
    ],
    "total": 1
  }
}
```

## 预定义脚本示例

### MySQL查询脚本

#### 1. 根据日期范围查询能源数据
**脚本代码：** `QUERY_ENERGY_BY_DATE`
**参数：**
- `startDate`: 开始日期 (字符串)
- `endDate`: 结束日期 (字符串)

**示例请求：**
```json
{
  "code": "QUERY_ENERGY_BY_DATE",
  "params": {
    "startDate": "2025-01-01",
    "endDate": "2025-01-31"
  }
}
```

#### 2. 根据状态和能源ID列表查询设备
**脚本代码：** `QUERY_DEVICE_BY_STATUS`
**参数：**
- `status`: 设备状态 (字符串)
- `energyIds`: 能源ID列表 (数组)

**示例请求：**
```json
{
  "code": "QUERY_DEVICE_BY_STATUS",
  "params": {
    "status": "1",
    "energyIds": [1, 2, 3]
  }
}
```

#### 3. 根据成本中心查询计量器具
**脚本代码：** `QUERY_MEASURE_BY_CENTER`
**参数：**
- `costCenterId`: 成本中心ID (数值)
- `status`: 状态 (字符串)

**示例请求：**
```json
{
  "code": "QUERY_MEASURE_BY_CENTER",
  "params": {
    "costCenterId": 1,
    "status": "1"
  }
}
```

### InfluxDB查询脚本

#### 1. 查询设备能耗趋势数据
**脚本代码：** `INFLUX_ENERGY_TREND`
**参数：**
- `bucket`: 数据桶名称 (字符串)
- `startTime`: 开始时间 (字符串，ISO格式)
- `endTime`: 结束时间 (字符串，ISO格式)
- `deviceId`: 设备ID (字符串)

**示例请求：**
```json
{
  "code": "INFLUX_ENERGY_TREND",
  "params": {
    "bucket": "DemoEMS",
    "startTime": "2025-01-01T00:00:00Z",
    "endTime": "2025-01-31T23:59:59Z",
    "deviceId": "device_001"
  }
}
```

#### 2. 查询功率统计数据
**脚本代码：** `INFLUX_POWER_STATS`
**参数：**
- `bucket`: 数据桶名称 (字符串)
- `startTime`: 开始时间 (字符串，ISO格式)

**示例请求：**
```json
{
  "code": "INFLUX_POWER_STATS",
  "params": {
    "bucket": "DemoEMS",
    "startTime": "2025-01-01T00:00:00Z"
  }
}
```

#### 3. 测试查询（修复日期参数问题）
**脚本代码：** `TEST`
**参数：**
- `bucket`: 数据桶名称 (字符串)
- `startTime`: 开始时间 (字符串，ISO格式)
- `endTime`: 结束时间 (字符串，ISO格式)
- `measurement`: 测量名称 (字符串)
- `device`: 设备ID (字符串)

**示例请求：**
```json
{
  "code": "TEST",
  "params": {
    "bucket": "EMS",
    "startTime": "2025-07-31T08:00:00+08:00",
    "endTime": "2025-07-31T09:00:00+08:00",
    "measurement": "Carbon",
    "device": "B160_D1097bd32bff0"
  }
}
```

## 参数类型支持

### 1. 字符串类型
自动添加单引号，转义特殊字符
```json
{
  "paramName": "value"
}
```

### 2. 数值类型
直接替换，支持整数、长整数、双精度
```json
{
  "paramName": 123
}
```

### 3. 布尔类型
转换为数据库对应值（1/0）
```json
{
  "paramName": true
}
```

### 4. 日期类型
转换为数据库对应格式
- **MySQL**: 添加单引号，格式为 `'2025-01-01 10:30:00'`
- **InfluxDB**: 不添加引号，支持ISO格式 `2025-07-31T08:00:00+08:00`

```json
{
  "paramName": "2025-01-01 10:30:00"
}
```

**InfluxDB时间格式示例：**
```json
{
  "startTime": "2025-07-31T08:00:00+08:00",
  "endTime": "2025-07-31T09:00:00+08:00"
}
```

### 5. 列表类型
转换为IN子句格式
```json
{
  "paramName": [1, 2, 3]
}
```

## 安全特性

### 1. SQL注入防护
- 危险关键字检测
- 特殊字符过滤
- 多语句检测
- 注释检测

### 2. 权限验证
- 脚本执行权限检查
- 用户角色验证

### 3. 超时控制
- 默认30秒查询超时
- 可配置超时时间

### 4. 结果限制
- 最大结果数量限制
- 防止内存溢出

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 查询成功 |
| 400 | 参数错误 |
| 403 | 权限不足 |
| 404 | 脚本不存在 |
| 500 | 服务器内部错误 |
| 408 | 查询超时 |

## 配置说明

在 `application.yml` 中可配置以下参数：

```yaml
ems:
  script:
    query:
      query-timeout: 30              # 查询超时时间（秒）
      max-result-size: 10000         # 最大结果数量
      enable-cache: true             # 是否启用缓存
      cache-expire-minutes: 10       # 缓存过期时间（分钟）
      enable-permission-check: true  # 是否启用权限验证
      max-parameter-count: 50        # 最大参数数量
```

### 配置属性使用说明

1. **query-timeout**: 控制单次查询的最大执行时间，防止长时间查询阻塞系统
2. **max-result-size**: 限制查询结果的最大数量，超过限制时自动截断结果
3. **enable-cache**: 启用脚本配置缓存，提高查询性能
4. **cache-expire-minutes**: 缓存过期时间，平衡性能和数据一致性
5. **enable-permission-check**: 启用权限验证，确保用户只能执行有权限的脚本
6. **max-parameter-count**: 限制单次查询的最大参数数量，防止参数过多导致性能问题

### 获取配置信息接口

**接口地址：** `GET /api/script/config/properties`

**响应结果：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "queryTimeout": 30,
    "maxResultSize": 10000,
    "enableCache": true,
    "cacheExpireMinutes": 10,
    "enablePermissionCheck": true,
    "maxParameterCount": 50
  }
}
```
